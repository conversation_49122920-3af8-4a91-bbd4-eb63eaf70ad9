// src/features/PartnersSection/PartnersSection.jsx
import React from 'react';
import styles from './PartnersSection.module.css';

// Import all the logos from the assets folder
import openAiLogo from '../../assets/logos/openai.webp';
import amazonLogo from '../../assets/logos/amazon.webp';
import googleLogo from '../../assets/logos/google.webp';
import anthropicLogo from '../../assets/logos/anthropic.webp';
import marriottLogo from '../../assets/logos/marriott.webp';
import shopifyLogo from '../../assets/logos/shopify.webp';
import airbnbLogo from '../../assets/logos/airbnb.webp';
import urbanOutfittersLogo from '../../assets/logos/urbanoutfitters.webp';

const logos = [
  { src: openAiLogo, alt: 'OpenAI' },
  { src: amazonLogo, alt: 'Amazon' },
  { src: googleLogo, alt: 'Google' },
  { src: anthropicLogo, alt: 'Anthropic' },
  { src: marriott<PERSON>ogo, alt: 'Marriott' },
  { src: shopifyLogo, alt: 'Shopify' },
  { src: airbnbLogo, alt: 'Airbnb' },
  { src: urbanOutfittersLogo, alt: 'Urban Outfitters' },
];

const PartnersSection = () => {
  return (
    <section className={styles.partnersSection}>
      <div className={styles.logoGrid}>
        {logos.map((logo, index) => (
          <img key={index} src={logo.src} alt={logo.alt} className={styles.logo} />
        ))}
      </div>
    </section>
  );
};

export default PartnersSection;