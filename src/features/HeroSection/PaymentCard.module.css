/* src/features/HeroSection/PaymentCard.module.css */
.card {
  background-color: white;
  color: black;
  border-radius: 1rem;
  padding: 1.5rem;
  width: 320px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
.price { font-weight: 600; margin-bottom: 1rem; }
.applePay {
  width: 100%;
  background-color: black;
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}
.divider {
  text-align: center;
  color: #888;
  font-size: 0.8rem;
  margin: 1rem 0;
  position: relative;
}
.divider::before, .divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 35%;
  height: 1px;
  background-color: #ddd;
}
.divider::before { left: 0; }
.divider::after { right: 0; }
.form { display: flex; flex-direction: column; gap: 0.75rem; }
.form input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.9rem;
}
.row { display: flex; gap: 0.75rem; }
.payButton {
  width: 100%;
  background-color: #0070f3;
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
}