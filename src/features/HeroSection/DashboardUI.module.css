/* src/features/HeroSection/DashboardUI.module.css */
.dashboard {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1rem;
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 600px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #888;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.headerIcons {
  display: flex;
  gap: 1rem;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.card {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
}

.card p {
  font-size: 0.8rem;
  color: #aaa;
  margin-bottom: 0.5rem;
}

.card h3 {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 1rem;
}

.chartPlaceholder {
  height: 50px;
  background: linear-gradient(to right, #333, #555);
  border-radius: 4px;
  opacity: 0.5;
}

.barChart {
  display: flex;
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
}
.barPaid { background-color: #0070f3; flex-grow: 5; }
.barOpen { background-color: #7928ca; flex-grow: 2; }
.barPastDue { background-color: #ff0080; flex-grow: 1; }