// src/features/HeroSection/DashboardUI.jsx
import React from 'react';
import styles from './DashboardUI.module.css';

const ChartPlaceholder = () => <div className={styles.chartPlaceholder}></div>;

const DashboardUI = () => {
  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <span>ROCKET RIDES</span>
        <div className={styles.headerIcons}>
          <span>👤</span>
          <span>⚙️</span>
        </div>
      </div>
      <div className={styles.grid}>
        <div className={styles.card}>
          <p>Net volume</p>
          <h3>$3,528,198.72</h3>
          <ChartPlaceholder />
        </div>
        <div className={styles.card}>
          <p>USD Balance</p>
          <h3>$553,257.51</h3>
        </div>
        <div className={styles.card}>
          <p>Net volume from sales</p>
          <h3>$39,274.29</h3>
          <ChartPlaceholder />
        </div>
        <div className={styles.card}>
          <p>Invoices</p>
          <div className={styles.barChart}>
            <div className={styles.barPaid}></div>
            <div className={styles.barOpen}></div>
            <div className={styles.barPastDue}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardUI;