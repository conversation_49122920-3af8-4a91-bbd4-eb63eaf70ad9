// src/features/HeroSection/HeroSection.jsx
import React from 'react';
import HeroAnimation from './HeroAnimation';
import styles from './HeroSection.module.css'; 

const HeroSection = () => {
  return (
    <section className={styles.heroSection}>
      <div className={styles.content}>
        <div className={styles.promoBanner}>
          <span>Sessions 2025 &middot; Watch on demand &gt;</span>
        </div>

        <h1 className={styles.headline}>
          Financial
          <br />
          infrastructure
          <br />
          to grow your
          <br />
          revenue
        </h1>

        <p className={styles.subheadline}>
          Join the millions of companies that use Stripe to accept payments online
          and in person, embed financial services, power custom revenue models,
          and build a more profitable business.
        </p>

        <div className={styles.ctaForm}>
          <input type="email" placeholder="Email address" />
          <button>Start now &gt;</button>
        </div>
      </div>

      <div className={styles.animationContainer}>
        <HeroAnimation />
      </div>
    </section>
  );
};

export default HeroSection;