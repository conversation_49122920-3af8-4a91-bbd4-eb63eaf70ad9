// src/features/HeroSection/PaymentCard.jsx
import React from 'react';
import styles from './PaymentCard.module.css';

const PaymentCard = () => {
  return (
    <div className={styles.card}>
      <p>Abstraction Magazine</p>
      <p className={styles.price}>$19 per month</p>
      <button className={styles.applePay}> Pay</button>
      <div className={styles.divider}>
        <span>Or pay with card</span>
      </div>
      <form className={styles.form}>
        <input type="email" placeholder="Email" />
        <input type="text" placeholder="Card information" />
        <div className={styles.row}>
          <input type="text" placeholder="MM / YY" />
          <input type="text" placeholder="CVC" />
        </div>
        <input type="text" placeholder="Country or region" />
        <button type="submit" className={styles.payButton}>Pay</button>
      </form>
    </div>
  );
};

export default PaymentCard;