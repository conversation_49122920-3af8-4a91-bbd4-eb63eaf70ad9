/* src/components/layout/Header.module.css */
.header {
  padding: 1rem 5rem; 
  position: absolute; 
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.navLeft, .navRight {
  display: flex;
  align-items: center;
  gap: 2rem; /* Space between items */
}

.navLinks {
  display: flex;
  list-style: none;
  gap: 1.5rem;
}

.navLinks a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease-in-out;
}

.navLinks a:hover {
  color: white;
}

.signInLink {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s ease-in-out;
}

.signInLink:hover {
  opacity: 0.8;
}