// src/components/layout/Header.jsx
import React from 'react';
import Logo from '../ui/Logo';
import Button from '../ui/Button';
import styles from './Header.module.css';

const Header = () => {
  return (
    <header className={styles.header}>
      <nav className={styles.nav}>
        <div className={styles.navLeft}>
          <Logo />
          <ul className={styles.navLinks}>
            <li><a href="#">Products</a></li>
            <li><a href="#">Solutions</a></li>
            <li><a href="#">Developers</a></li>
            <li><a href="#">Resources</a></li>
            <li><a href="#">Pricing</a></li>
          </ul>
        </div>
        <div className={styles.navRight}>
          <a href="#" className={styles.signInLink}>Sign in &gt;</a>
          <Button variant="primary">Contact sales &gt;</Button>
        </div>
      </nav>
    </header>
  );
};

export default Header;