# ===================================================================
#  Gitignore for Modern React Frontend Projects (Vite, CRA, etc.)
#  - Sections are logically grouped for readability and maintenance.
# ===================================================================

# ---------------------------
#  1. Dependencies & Lock Files
#    These are installed via package managers (npm, yarn, pnpm)
#    and should not be part of the repository.
# ---------------------------
/node_modules
/bower_components

# NOTE ON LOCK FILES:
# Unlike node_modules, lock files (package-lock.json, yarn.lock, pnpm-lock.yaml)
# SHOULD BE COMMITTED to your repository. They ensure that every developer
# and the CI/CD pipeline installs the exact same dependency versions,
# leading to deterministic and reliable builds.

# ---------------------------
#  2. Build & Production Output
#    Compiled, bundled, or transpiled code generated for deployment.
# ---------------------------
/build
/dist
/out
/.next

# ---------------------------
#  3. Secrets & Environment Variables 🔒
#    This is the most critical section for security.
#    NEVER commit files containing secrets, API keys, or credentials.
# ---------------------------
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# It's a best practice to commit an example file.
# !.env.example

# ---------------------------
#  4. Logs, Caches, and Temporary Files 🛠️
#    Files generated by tools for debugging, error reporting, or performance.
# ---------------------------
npm-debug.log*
yarn-debug.log
yarn-error.log
pnpm-debug.log*

# Caches from various tools
.npm
.eslintcache
.vite
.webpack-cache

# ---------------------------
#  5. Testing & Coverage
#    Reports generated by running tests.
# ---------------------------
/coverage
.jest-cache

# ---------------------------
#  6. IDE & Editor Configuration
#    Personal settings for your code editor.
# ---------------------------
# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
!.vscode/launch.json

# JetBrains (WebStorm, IntelliJ, etc.)
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# ---------------------------
#  7. Operating System Files
#    System-specific files that have no place in a project repo.
# ---------------------------
# macOS
.DS_Store
._*
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# ---------------------------
#  8. Miscellaneous
#    Other files that are often generated locally.
# ---------------------------
*.pem
*.key